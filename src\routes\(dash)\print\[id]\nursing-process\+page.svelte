<script lang="ts">
	import DDMMYYYYFormat from '$lib/coms/DDMMYYYYFormat.svelte';
	import Name from '$lib/coms/Name.svelte';
	import { calculateAge } from '$lib/helper';
	import type { PageServerData } from './$types';
	interface Props {
		data: PageServerData;
	}

	let { data }: Props = $props();
	let { get_progress_note, get_clinic_info } = $derived(data);
</script>

{#each get_progress_note?.activeDepartment || [] as item}
	{@const get_active_bed = item.activeBed[0]}
	<h5 class="kh_font_muol_light text-center pt-1">សាលាកប័ត្រផ្ទេរពត៌មាន</h5>
	<h6>
		<span>មន្ទីរពេទ្យ {get_clinic_info?.title_khm}</span>,
		<span>ផ្នែក {item?.department?.products ?? ''}</span>,
		<span>
			{get_active_bed?.bed?.ward?.ward ?? ''},
			{get_active_bed?.bed?.room?.room ?? ''},
			{get_active_bed?.bed?.bed ?? ''}
		</span>
	</h6>
	<h6>
		<span>
			{'នាមត្រកូលនិងនាមខ្លួន'}
			{get_progress_note?.patient?.name_khmer}
			{`( ${get_progress_note?.patient?.name_latin} )`}
		</span>
		<span>
			{'អាយុ'}
			{calculateAge(get_progress_note?.patient?.dob)}
		</span>
		<span>
			{'ភេទ'}
			{get_progress_note?.patient.gender}
		</span>
		<span>#ID {`PT${get_progress_note?.patient_id}`},{`VSI${get_progress_note?.id}`} </span>
	</h6>
	<h6>
		<span>
			{'សម្រាក់ចាប់ពី'}
			<DDMMYYYYFormat date={item.datetime_in} />
		</span>
		<span>
			{'រហូតដល់'}
			<DDMMYYYYFormat date={item.datetime_out} />
		</span>
	</h6>
	<table class="table mb-0 table-bordered table-hover text-wrap text-break table-light">
		<thead class="table-active">
			<tr class="text-center">
				<th style="width: 10%;">
					<div>ថ្ងៃខែឆ្នាំ</div>
					<div>និងម៉ោង</div>
				</th>
				<th>{'ការប៉ាន់ប្រមាណ'}</th>
				<th>
					<div>
						{'បញ្ហាសុខភាព'}
					</div>
					<div>
						{'ដែលត្រូវថែទាំ'}
					</div>
				</th>
				<th>
					<div>
						{'សកម្មភាព'}
					</div>
					<div>
						{'ដែលបានអនុវត្តន៍'}
					</div>
				</th>
				<th>{'ការវាយតម្លៃ'}</th>
				<th style="width: 15%;">
					<div>ឈ្មោះ និង ហត្ថលេខា</div>
					<div>របស់គិលានុបដ្ឋាក-យិកា</div>
				</th>
			</tr>
		</thead>
		<tbody>
			{#each get_progress_note?.nursingProcess || [] as item_1}
				{#if item_1.active_department_id === item.id}
					<tr class="text-center">
						<td>
							<DDMMYYYYFormat date={item_1.datetime} />
						</td>

						<td>{item_1.accessment ?? ''}</td>
						<td>{item_1.health_problems ?? ''}</td>
						<td>{item_1.actions ?? ''} </td>
						<td>{item_1.evolution ?? ''}</td>
						<td>
							<Name name={item_1?.nursingSign} />
						</td>
					</tr>
				{/if}
			{/each}
		</tbody>
	</table>
	<br />
{/each}

<style scoped>
	@media print {
		@page {
			size: A4 landscape;
			margin-top: 10mm;
		}

		/* img
	 {
		page-break-inside: avoid;
	} */
	}
</style>
