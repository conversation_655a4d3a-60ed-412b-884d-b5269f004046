<script lang="ts">
	import type { ActionData, PageServerData } from './$types';
	import SubmiteSearch from '$lib/coms/SubmiteSearch.svelte';
	import DDMMYYYYFormat from '$lib/coms/DDMMYYYYFormat.svelte';
	import Currency from '$lib/coms/Currency.svelte';
	import ConfirmModal from '$lib/coms-form/ConfirmModal.svelte';
	import ChargeGeneral from '$lib/coms-billing/ChargeGeneral.svelte';
	import ProductAddToCard from '$lib/coms-billing/ProductAddToCard.svelte';
	import Form from '$lib/coms-form/Form.svelte';
	import { store } from '$lib/store/store.svelte';
	import { locale } from '$lib/translations/locales.svelte';
	import SearchProductSubmit from '$lib/coms-form/SearchProductSubmit.svelte';
	import SelectParam from '$lib/coms-form/SelectParam.svelte';
	import DeleteModal from '$lib/coms/DeleteModal.svelte';
	import { enhance } from '$app/forms';
	import { page } from '$app/state';
	import SetBack from '$lib/coms/SetBack.svelte';
	interface Props {
		data: PageServerData;
		form: ActionData;
	}

	let { data, form }: Props = $props();
	let {
		get_products,
		charge_on_general,
		get_billing,
		get_currency,
		get_pre_billing,
		get_billings_due,
		get_categories,
		get_patients,
		get_groups
	} = $derived(data);

	let inerHight: string = $derived(
		(Number(store.inerHight.replace('px', '')) - 124).toString().concat('px')
	);
	let items = $derived(Number(charge_on_general?.productOrder.length || 0));
	let patient_id = $state(page.url.searchParams.get('patient_id') || '');
	let billing_id: number | null = $state(null);
</script>

<div class="row g-1">
	<div class="col-md-5">
		<div class="card rounded-0 bg-light">
			<div class="card-header">
				<div class="row g-1 justify-content-between align-items-center">
					<div class="col">
						<form use:enhance method="post" action="?/update_billing_customer">
							<input type="hidden" value={get_billing?.id} name="billing_id" />
							<SelectParam
								submit={true}
								name="patient_id"
								value={patient_id}
								placeholder={get_billing?.patient?.name_khmer?.concat(
									` ${get_billing?.patient?.name_latin}`
								) || locale.T('customer')}
								q_name="patient_q"
								items={get_patients.map((e) => ({
									id: e.id,
									name: e.name_khmer?.concat(` ${e.name_latin}`)
								}))}
							/>
						</form>
					</div>
					<div class="col-auto">
						<div class="btn-group my-0">
							<button
								data-bs-toggle="modal"
								data-bs-target="#create_patients"
								aria-label="card"
								type="button"
								class="btn btn-light my-0"
							>
								<i class="fa-solid fa-user"></i>
							</button>
							<SetBack class="btn btn-light my-0" href="/patient/create">
								<i class="fa-solid fa-user-plus"></i>
							</SetBack>
							<button
								aria-label="card"
								type="button"
								data-bs-toggle="modal"
								data-bs-target="#billing_saved"
								class="btn btn-warning float-end position-relative"
							>
								<i class="fa-regular fa-circle-pause"></i>
								{#if get_pre_billing.length}
									<span
										class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger"
									>
										{get_pre_billing.length}
									</span>
								{/if}
							</button>
						</div>
					</div>
					<div class="col-12 pt-2">
						<SearchProductSubmit
							placeholder="ស្វែករកតាមរយៈផលិតផល"
							q_name="q"
							items={get_products.map((e) => ({ id: e.id, name: e.products, price: e.price }))}
							action="?/create_product_order"
							billing_id={get_billing?.id}
						>
							<input type="hidden" value={get_billing?.id} name="billing_id" />
						</SearchProductSubmit>
					</div>
				</div>
			</div>
			<div style="height: {inerHight}" class="card-body table-responsive p-0 m-0">
				<table class="table table-bordered table-sm text-nowrap table-light">
					<thead class="sticky-top top-0 bg-light table-active">
						<tr class="text-center">
							<th style="width: 45%;">{locale.T('product_name')}</th>
							<th style="width: 15%;">{locale.T('price')}</th>
							<th style="width: 10%;">{locale.T('qty')}</th>
							<th style="width: 15%;">{locale.T('sub_total')} </th>
							<th style="width: 5%;">X</th>
						</tr>
					</thead>
					<tbody>
						<ChargeGeneral
							data={{
								charge_on_general: charge_on_general,
								get_currency: get_currency
							}}
						/>
					</tbody>
				</table>
			</div>

			<div class="border-0 bg-primary-subtle py-1 fs-6 mb-1">
				<div class="row">
					<div class="col text-start mx-2">{locale.T('quantity_of_goods')}</div>
					<div class="col text-end mx-2">{items} {locale.T('items')}</div>
				</div>
			</div>
			<div class="border-0 bg-primary-subtle py-1 fs-6 mb-1">
				<div class="row">
					<div class="col text-start mx-2">
						{locale.T('total')}
						{get_currency?.exchang_to}
					</div>
					<div class="col text-end mx-2">
						<Currency
							class=""
							{get_currency}
							amount={get_billing?.amount}
							symbol={get_currency?.exchang_to}
						/>
					</div>
				</div>
			</div>
			<div class="border-0 bg-primary-subtle py-1 fs-6 mb-1">
				<div class="row">
					<div class="col text-start mx-2">
						{locale.T('total')}
						{get_currency?.currency}
					</div>
					<div class="col text-end mx-2">
						<Currency class="" amount={get_billing?.total} symbol={get_currency?.currency} />
					</div>
				</div>
			</div>

			{#each get_billings_due as item (item.id)}
				<div class=" border-0 bg-primary-subtle py-1 fs-6 mb-1">
					<div class="row">
						<div class="col text-start mx-2">
							{locale.T('previous_debt')}
							<DDMMYYYYFormat date={item.created_at} style="date" />
						</div>
						<div class="col text-end mx-2">
							<a href="/billing/repay?billing_id={item.id}">
								<Currency class="fs-6" amount={item.balance} symbol={get_currency?.currency} /></a
							>
						</div>
					</div>
				</div>
			{/each}
			<div class="row g-0">
				<div class="col">
					<a href="/billing/report" class="btn rounded-0 btn-info btn-lg w-100">
						<i class="fa-solid fa-rotate-left"></i>{locale.T('back')}</a
					>
				</div>
				<div class="col">
					<Form action="?/clear_billing" method="post">
						{#each charge_on_general?.productOrder || [] as item (item.id)}
							<input type="hidden" name="product_order_id" value={item.id} />
						{/each}
						<input type="hidden" name="billing_id" value={get_billing?.id} />
						<button
							disabled={Number(get_billing?.amount) <= 0}
							aria-label="card"
							type="submit"
							class="btn rounded-0 btn-danger btn-lg w-100"
						>
							<i class="fa-solid fa-broom"></i>
							{locale.T('clear')}
						</button>
					</Form>
				</div>
				<div class="col">
					<button
						disabled={Number(get_billing?.amount) <= 0}
						aria-label="card"
						type="button"
						data-bs-toggle="modal"
						data-bs-target="#hold"
						class="btn rounded-0 btn-warning btn-lg w-100"
					>
						<i class="fa-regular fa-circle-pause"></i>
						{locale.T('hold')}
					</button>
				</div>
				<div class="col">
					{#if Number(get_billing?.amount) <= 0}
						<button class="btn rounded-0 btn-success btn-lg w-100" disabled>
							<i class="fa-solid fa-comments-dollar"></i> {locale.T('billing')}</button
						>
					{:else}
						<SetBack
							href="/billing/pos/{get_billing?.id}/checkout"
							class="btn rounded-0 btn-success btn-lg w-100"
						>
							<i class="fa-solid fa-comments-dollar"></i>
							{locale.T('billing')}
						</SetBack>
					{/if}
				</div>
			</div>
		</div>
	</div>
	<div class="col-md-7">
		<ProductAddToCard
			data={{
				get_products: get_products,
				get_categories: get_categories,
				get_currency: get_currency,
				get_groups: get_groups
			}}
			billing_id={Number(get_billing?.id)}
		/>
	</div>
</div>
<!-- Modal Hold -->
<div
	class="modal fade"
	id="hold"
	data-bs-backdrop="static"
	data-bs-keyboard="false"
	tabindex="-1"
	aria-labelledby="create_hold"
	aria-hidden="true"
>
	<div class="modal-dialog">
		<div class="modal-content">
			<Form
				fnSuccess={() => document.getElementById('close_hold')?.click()}
				action="?/hold"
				method="post"
			>
				<div class="modal-header">
					<h1 class="modal-title fs-6" id="create_hold">{locale.T('hold')}</h1>
					<button
						id="close_hold"
						type="button"
						class="btn-close"
						data-bs-dismiss="modal"
						aria-label="Close"
					></button>
				</div>
				<div class="modal-body">
					<div class="row">
						<div class="col-12">
							<div class=" pb-3">
								<label for="note">{locale.T('note')}</label>
								<textarea value={get_billing?.note ?? ''} class="form-control" name="note" id="note"
								></textarea>
							</div>
						</div>
					</div>
				</div>
				<div class="modal-footer">
					<button type="submit" class="btn btn-primary">{locale.T('save')}</button>
				</div>
			</Form>
		</div>
	</div>
</div>
<!-- Modal Customer -->
<div
	class="modal fade"
	id="create_patients"
	data-bs-backdrop="static"
	data-bs-keyboard="false"
	tabindex="-1"
	aria-labelledby="create_patientsLabel"
	aria-hidden="true"
>
	<div class="modal-dialog">
		<div class="modal-content">
			<Form
				fnSuccess={() => document.getElementById('close_create_patients')?.click()}
				action="?/create_patient"
				method="post"
			>
				<div class="modal-header">
					<h1 class="modal-title fs-6" id="create_patientsLabel">{locale.T('customer')}</h1>
					<button
						id="close_create_patients"
						type="button"
						class="btn-close"
						data-bs-dismiss="modal"
						aria-label="Close"
					></button>
				</div>
				<div class="modal-body">
					<div class="row">
						<div class="col-12">
							<div class=" pb-3">
								<label for="name_khmer">{locale.T('name_khmer')}</label>
								<input
									class:border-danger={form?.name_khmer}
									name="name_khmer"
									type="text"
									class="form-control"
									id="name_khmer"
								/>
							</div>
						</div>
						<div class="col-12">
							<div class=" pb-3">
								<label for="name_latin">{locale.T('name_latin')}</label>
								<input name="name_latin" type="text" class="form-control" id="name_latin" />
							</div>
						</div>
						<div class="col-12">
							<div class=" pb-3">
								<label for="telephone">{locale.T('contact')}</label>
								<input name="telephone" type="text" class="form-control" id="telephone" />
							</div>
						</div>
						<div class="col-12">
							<div class=" pb-3">
								<label for="gender">{locale.T('gender')}</label>
								<select name="gender" class="form-control" id="gender">
									<option value="Other">{locale.T('unknown')}</option>
									<option value="Male">{locale.T('male')}</option>
									<option value="Female">{locale.T('female')}</option>
								</select>
							</div>
						</div>
					</div>
				</div>
				<div class="modal-footer">
					<button type="submit" class="btn btn-primary">{locale.T('save')}</button>
				</div>
			</Form>
		</div>
	</div>
</div>
<!-- Modal Not yet checkout billing -->
<div
	class="modal fade"
	id="billing_saved"
	data-bs-backdrop="static"
	data-bs-keyboard="false"
	tabindex="-1"
	aria-labelledby="create_patientsLabel"
	aria-hidden="true"
>
	<div class="modal-dialog modal-lg">
		<div class="modal-content">
			<div class="modal-header">
				<h1 class="modal-title fs-6" id="create_patientsLabel">{locale.T('cart_saved')}</h1>
				<button
					id="close_billing_saved"
					type="button"
					class="btn-close"
					data-bs-dismiss="modal"
					aria-label="Close"
				></button>
			</div>
			<div class="modal-body">
				<div class="row">
					<div class="table">
						<table class="table table-light">
							<thead>
								<tr>
									<th>{locale.T('date')}</th>
									<th>{locale.T('note')}</th>
									<th>{locale.T('name')}</th>
									<th>{locale.T('contact')}</th>
									<th>{locale.T('total')}</th>
									<th>{locale.T('goods')}</th>
								</tr>
							</thead>
							<tbody>
								{#each get_pre_billing as item, index (item.id)}
									{@const charge_on_general = item.charge?.find((e) => e.charge_on === 'general')}
									<tr>
										<td>
											<DDMMYYYYFormat style="date" date={item.created_at} />
										</td>
										<td>
											<div class="text-break">{item.note ?? ''}</div>
										</td>
										<td>
											{item.patient?.name_khmer ?? ''}

											{item.patient?.name_latin ?? ''}
										</td>
										<td>{item.patient?.telephone ?? ''}</td>
										<td>
											<Currency amount={item.amount} symbol={get_currency?.currency} />
										</td>
										<td>
											<a
												href="/billing/pos/{item.id}"
												onclick={() => document.getElementById('close_billing_saved')?.click()}
											>
												<i class="fa-solid fa-clock-rotate-left"></i>
												{charge_on_general?.productOrder.length ?? 0}
												{locale.T('items')}
											</a>
											<button
												aria-label="deletemodal"
												onclick={() => (billing_id = item.id)}
												type="button"
												class="btn btn-link text-danger"
												data-bs-toggle="modal"
												data-bs-target="#delete_modal"
												>{locale.T('delete_')}
											</button>
										</td>
									</tr>
								{/each}
							</tbody>
						</table>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
<DeleteModal action="?/delete_billing" id={billing_id} />
