<script lang="ts">
	interface Props {
		html?: string | null | undefined;
		contenteditable?: boolean;
		style?: string;
	}

	let { html = $bindable(''), contenteditable = false,style }: Props = $props();
</script>

{#if contenteditable}
	<div style="max-width:100%;{style}" contenteditable="true" bind:innerHTML={html}></div>
{:else}
	<div style="max-width:100%;{style}" contenteditable="false" bind:innerHTML={html}></div>
{/if}

