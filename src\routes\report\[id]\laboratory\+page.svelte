<script lang="ts">
	import type { PageServerData } from './$types';
	import { dobToAge } from '$lib/helper';
	import Athtml from '$lib/coms/Athtml.svelte';
	import DDMMYYYYFormat from '$lib/coms/DDMMYYYYFormat.svelte';
	import ClinichInfo from '$lib/coms-report/ClinichInfo.svelte';
	import { page } from '$app/state';
	import Sign from '$lib/coms-report/Sign.svelte';
	interface Props {
		data: PageServerData;
	}

	let { data }: Props = $props();
	let {
		get_visit,
		sort_laboraytor,
		get_clinic_info,
		get_imagers,
		sort_duplicateName,
		get_upload,
		get_inputer_sign
	} = $derived(data);

	let age_p_visit = $derived(
		dobToAge(get_visit?.patient?.dob ?? '', get_visit?.date_checkup ?? '')
	);
	let isPrint = page.url.searchParams.get('print');
	$effect(() => {
		document.addEventListener('keydown', function (event) {
			window.scrollTo({ top: 0, behavior: 'smooth' });
			if (event.ctrlKey && event.key === 'p') {
				// event.preventDefault();
				// alert('View only');
			}
		});
		if (isPrint === 'true') {
			setTimeout(async () => {
				window.print();
				window.close();
			}, 300);
		}
	});
</script>

<div class="header">
	<ClinichInfo data={{ get_clinic_info, get_upload }} />
	<div class="border p-2 pb-0">
		<table class=" table table-sm table-borderless">
			<thead>
				<tr class="p-0 m-0">
					<td class="text-bold kh_font_battambang p-0 m-0">ឈ្មោះជាខ្មែរ</td>
					<td class="p-0 m-0"> : </td>
					<td class="p-0 m-0">{get_visit?.patient?.name_khmer}</td>
					<td class="text-bold kh_font_battambang p-0 m-0">ភេទ</td>
					<td class="p-0 m-0"> : </td>
					<td class="p-0 m-0"
						>{get_visit?.patient?.gender.toLowerCase() === 'female'
							? 'ស្រី'
							: get_visit?.patient?.gender.toLowerCase() === 'male'
								? 'ប្រុស'
								: ''}</td
					>
					<td class="text-bold kh_font_battambang p-0 m-0">លេខកូដ</td>
					<td class="p-0 m-0"> : </td>
					<td class="p-0 m-0">PT{get_visit?.patient_id}, LB{get_visit?.laboratory?.id ?? ''}</td>
				</tr>
				<tr class="p-0 m-0">
					<td class="text-bold kh_font_battambang p-0 m-0">ឈ្មោះឡាតាំង</td>
					<td class="p-0 m-0"> : </td>
					<td class="p-0 m-0">{get_visit?.patient?.name_latin}</td>
					<td class="text-bold kh_font_battambang p-0 m-0">អាយុ</td>
					<td class="p-0 m-0"> : </td>
					<td class="p-0 m-0">
						{age_p_visit}
					</td>
					<td style="font-size: 110%;" class="text-bold en_font_times_new_roman p-0 m-0"
						>Collection Date</td
					>
					<td class="p-0 m-0"> : </td>
					<td class="p-0 m-0">
						<DDMMYYYYFormat date={get_visit?.date_checkup} />
					</td>
				</tr>
				<tr class="p-0 m-0">
					<td class="text-bold kh_font_battambang p-0 m-0">គ្រូពេទ្យស្នើរសុំ</td>
					<td class="p-0 m-0"> : </td>
					<td class="p-0 m-0">{get_visit.staff.title?.kh} {get_visit?.staff?.name_khmer ?? ''}</td>
					<td style="font-size: 110%;" class="text-bold en_font_times_new_roman p-0 m-0">Visit</td>
					<td class="p-0 m-0"> : </td>
					<td style="font-size: 110%;" class="en_font_times_new_roman p-0 m-0"
						>{get_visit?.checkin_type ?? ''}</td
					>
					<td style="font-size: 110%;" class="text-bold en_font_times_new_roman p-0 m-0"
						>Report Date</td
					>
					<td class="p-0 m-0"> : </td>
					<td class="p-0 m-0">
						{#if get_visit?.laboratory?.finish_datetime}
							<DDMMYYYYFormat date={get_visit?.laboratory?.finish_datetime} />
						{/if}
					</td>
				</tr>
				<tr class="p-0 m-0">
					<td class="text-bold kh_font_battambang p-0 m-0">ទំនាក់ទំនង</td>
					<td class="p-0 m-0"> : </td>
					<td style="font-size: 110%;" class="en_font_times_new_roman p-0 m-0"
						>{get_visit?.patient?.telephone ?? ''}</td
					>
					<td class="text-bold kh_font_battambang p-0 m-0">អាសយដ្ឋាន</td>
					<td class="p-0 m-0"> : </td>
					<td colspan="6" class="kh_font_battambang p-0 m-0">
						{get_visit?.patient?.village?.type ?? ''}
						{get_visit?.patient?.village?.name_khmer.concat(',') ?? ''}
						{get_visit?.patient?.commune?.type ?? ''}
						{get_visit?.patient?.commune?.name_khmer.concat(',') ?? ''}
						{get_visit?.patient?.district?.type ?? ''}
						{get_visit?.patient?.district?.name_khmer.concat(',') ?? ''}
						{get_visit?.patient?.provice?.type ?? ''}
						{get_visit?.patient?.provice?.name_khmer ?? ''}
					</td>
				</tr>
			</thead>
		</table>
	</div>
</div>
<table class="w-100">
	<thead>
		<tr>
			<td>
				<div class="header-space">&nbsp;</div>
			</td>
		</tr>
	</thead>
	<tbody>
		<tr>
			<td>
				<u>
					<h1 style="font-size: 130%;" class="text-center kh_font_muol">លទ្ធផលមន្ទីរពិសោធន៍</h1>
					<h1 style="font-size: 100%;" class="text-center en_font_times_new_roman">
						Laboratory Report
					</h1>
				</u>
			</td>
		</tr>
		{#each sort_duplicateName || [] as item_0, group_index (item_0.id)}
			{@const group_counter = { value: 0 }}
			<tr>
				<td>
					<table class="table text-wrap table-sm table-borderless border p-0 m-0">
						<thead>
							<tr>
								<td class="text-center table-secondary" colspan="6">
									<span
										class="en_font_times_new_roman text-decoration-underline"
										style="font-size: 120%;color:#0000FF"
									>
										{item_0.product?.laboratoryGroup?.laboratory_group ?? ''}
									</span>
									<div>
										{#each sort_laboraytor || [] as ie}
											{#if item_0.product?.laboratory_group_id === ie.product?.laboratory_group_id}
												<span>{ie.product?.products},</span>
											{/if}
										{/each}
									</div>
								</td>
							</tr>
							<tr style="font-size: 120%;" class="text-center en_font_times_new_roman">
								<th style="width: 5%;">ល.រ</th>
								<th class="text-start" style="width: 50%;">&nbsp;&nbsp; ឈ្មេាះតេស្តពិសោធន៏</th>
								<th style="width: 15%;">លទ្ធផល</th>
								<th style="width: 15%;">ខ្នាត</th>
								<th style="width: 15%;">តម្លៃយោង</th>
							</tr>
						</thead>

						<tbody class="table-sm">
							{#each sort_laboraytor || [] as item_1, index_1 (item_1.id)}
								{#if item_0.product?.laboratory_group_id === item_1.product?.laboratory_group_id}
									{#each item_1.laboratoryResult as item_2, index_2 (item_2.id)}
										{@const check_result = item_2?.result ?? ''}
										{@const group_item_index = ++group_counter.value}
										<tr class="text-center kh_font_battambang">
											<td style="vertical-align: middle;" class="p-0 m-0">{group_item_index}</td>
											<td class="text-start ps-4">
												<div style="font-weight: normal;" class="p-0 m-0 input_document text-start">
													<span style="font-size: 120%;" class="en_font_times_new_roman"
														>{item_2.parameter?.parameter}
													</span>

													<Athtml html={item_2?.parameter?.description ?? ''} />
												</div>
											</td>
											<td style="font-size: 120%;" class="p-0 m-0 en_font_times_new_roman">
												{#if check_result === 'Positive' || check_result === 'POSITIVE' || check_result === '1/160' || check_result === '1/320' || check_result === '+' || check_result === '++' || check_result === '+++' || check_result === '++++'}
													<span class="en_font_times_new_roman" style="color: #FF0000;">
														{item_2?.result ?? ''}
													</span>
												{:else if Number(check_result) >= Number(item_2.parameter?.mini) && Number(check_result) <= Number(item_2.parameter?.maxi)}
													<span class="en_font_times_new_roman"> {check_result}</span>
												{:else if Number(check_result) < Number(item_2.parameter?.mini)}
													<span class="en_font_times_new_roman" style="color: #0000FF;"
														>{check_result} L</span
													>
												{:else if Number(check_result) > Number(item_2.parameter?.maxi)}
													<span class="en_font_times_new_roman" style="color: #FF0000;"
														>{check_result} H</span
													>
												{:else}
													<span class="en_font_times_new_roman" style="color: #0000FF;">
														{check_result ?? ''}
													</span>
												{/if}
											</td>
											<td style="font-size: 120%;" class="p-0 m-0 en_font_times_new_roman">
												<Athtml html={item_2.parameter?.paraUnit?.unit ?? ''} />
											</td>
											<td style="font-size: 120%;" class="p-0 m-0 en_font_times_new_roman">
												<span class="p-0 m-0 en_font_times_new_roman">
													{item_2.parameter?.mini === 0
														? ''
														: item_2.parameter?.mini?.toLocaleString('en-US')}</span
												>
												<span class="p-0 m-0 en_font_times_new_roman">
													{item_2.parameter?.sign}</span
												>
												<span class="p-0 m-0 en_font_times_new_roman">
													{item_2.parameter?.maxi === 0
														? ''
														: item_2.parameter?.maxi?.toLocaleString('en-US')}</span
												>
											</td>
										</tr>
									{/each}
								{/if}
							{/each}
						</tbody>
					</table>
				</td>
			</tr>
		{/each}
		<tr>
			<td>
				<div class="row">
					{#each get_imagers || [] as item}
						<div class="col-6 p-2">
							<img
								class="img-fluid"
								style="width: 100%;height: 100%;"
								src={item.filename}
								alt="some alt text"
							/>
						</div>
					{/each}
				</div>
			</td>
		</tr>
		<tr>
			<td>
				{#if get_visit?.laboratory?.doctor_comment?.length}
					<div class=" border p-2 m-0">
						<h5>Comment</h5>
						<Athtml html={get_visit?.laboratory?.doctor_comment ?? ''} />
					</div>
				{/if}
			</td>
		</tr>
	</tbody>
	<tfoot>
		<tr>
			<td>
				<div class="footer-space">&nbsp;</div>
			</td>
		</tr>
	</tfoot>
</table>
<div class="footer pb-2">
	<Sign
		left={{
			date: get_visit?.laboratory?.finish_datetime,
			role: `ត្រួតពិនិត្យដោយ`
		}}
		right={{
			date: get_visit?.laboratory?.finish_datetime,
			img: get_visit?.staff?.sign?.filename,
			name: get_visit?.staff?.name_khmer,
			role: `បុគ្គលិកមន្ទីរពិសោធន៍`
		}}
		qr={page.url.href}
	/>
	<div>
		<hr />
		<h6 style="color:#0000FF" class="text-center">
			{get_clinic_info?.address ?? ''}
		</h6>
	</div>
</div>

<style>
	@media print {
		.footer,
		.footer-space {
			height: 330px;
		}
		.header,
		.header-space {
			height: 320px;
		}
	}

	td,
	th {
		vertical-align: bottom;
	}
</style>
