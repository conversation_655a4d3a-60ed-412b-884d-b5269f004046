<script lang="ts">
	import { locale } from '$lib/translations/locales.svelte';
	import type { Staff } from '$lib/type';
	type NameOnly = Pick<Staff, 'name_latin' | 'name_khmer'>;
	interface Props<T extends NameOnly = NameOnly> {
		name?: T | null;
		class?: string;
		both?: boolean;
	}
	let { both, name, class: className = '' }: Props<NameOnly> = $props();
</script>

{#if both}
	<span class={className}>
		{name?.name_khmer}
	</span>
	<br />
	<span class={className}>
		{name?.name_latin}
	</span>
{:else if locale.L === 'km'}
	<span class={className}>
		{name?.name_khmer}
	</span>
{:else}
	<span class={className}>
		{name?.name_latin || name?.name_khmer}
	</span>
{/if}
