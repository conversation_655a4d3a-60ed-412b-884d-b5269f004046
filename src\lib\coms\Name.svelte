<script lang="ts" generics="T extends { name_khmer?: string | null; name_latin?: string | null }">
	import { locale } from '$lib/translations/locales.svelte';

	export interface Props {
		staff?: T | null;
		name_khmer?: string | null;
		name_latin?: string | null;
		class?: string;
	}

	let { staff, name_khmer, name_latin, class: className = '' }: Props = $props();

	// Use individual props if provided, otherwise use staff object
	const displayNameKhmer = name_khmer ?? staff?.name_khmer;
	const displayNameLatin = name_latin ?? staff?.name_latin;
</script>

{#if locale.L === 'en'}
	<span class={className}>
		{staff?.name_latin || staff?.name_khmer}
	</span>
{/if}
{#if locale.L === 'km'}
	<span class={className}>
		{staff?.name_khmer}
	</span>
{/if}
