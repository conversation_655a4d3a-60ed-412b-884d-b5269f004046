<script lang="ts">
	import type { PageServerData } from './$types';
	import DDMMYYYYFormat from '$lib/coms/DDMMYYYYFormat.svelte';
	import Currency from '$lib/coms/Currency.svelte';
	import ConfirmModal from '$lib/coms-form/ConfirmModal.svelte';
	import ChargeLaboratory from '$lib/coms-billing/ChargeLaboratory.svelte';
	import ChargeImagerie from '$lib/coms-billing/ChargeImagerie.svelte';
	import ChargePrescription from '$lib/coms-billing/ChargePrescription.svelte';
	import ChargeService from '$lib/coms-billing/ChargeService.svelte';
	import ChargeVaccine from '$lib/coms-billing/ChargeVaccine.svelte';
	import ChargeGeneral from '$lib/coms-billing/ChargeGeneral.svelte';
	import { store } from '$lib/store/store.svelte';
	import { locale } from '$lib/translations/locales.svelte';
	import SearchProductSubmit from '$lib/coms-form/SearchProductSubmit.svelte';
	interface Props {
		data: PageServerData;
	}

	let { data }: Props = $props();
	let {
		get_products,
		charge_on_imagerie,
		charge_on_laboratory,
		charge_on_general,
		charge_on_prescription,
		charge_on_service,
		get_billing,
		get_currency,
		charge_on_vaccine,
		get_payment_types,
		get_billings_due
	} = $derived(data);

	let inerHight: string = $derived(
		(Number(store.inerHight.replace('px', '')) - 110).toString().concat('px')
	);
	let items = $derived(
		Number(charge_on_imagerie?.productOrder.length || 0) +
			Number(charge_on_laboratory?.productOrder.length || 0) +
			Number(charge_on_general?.productOrder.length || 0) +
			Number(charge_on_prescription?.productOrder.length || 0) +
			Number(charge_on_vaccine?.productOrder.length || 0) +
			Number(charge_on_service?.productOrder.length || 0)
	);
</script>

<div class="row">
	<div class="col-sm-8">
		<h4>
			<span>{locale.T('patient_name')}</span>
			<span class="text-primary">
				@{get_billing?.patient?.name_khmer},
				{get_billing?.patient?.name_latin}
			</span>
			<span>
				<DDMMYYYYFormat date={get_billing?.visit?.date_checkup} />
			</span>
		</h4>
	</div>
	<div class="col-sm-4">
		<ol class="breadcrumb justify-content-end">
			<li class="breadcrumb-item">
				<a href="/dashboard" class="btn btn-link p-0 text-secondary"
					><i class="fas fa-tachometer-alt"></i>
					{locale.T('home')}
				</a>
			</li>
			<li class="breadcrumb-item">
				<a href="/billing/opd" class="btn btn-link p-0 text-secondary"
					><i class="fas fa-money-bills"></i>
					{locale.T('billing')}
				</a>
			</li>
			<li class="breadcrumb-item">
				<a href={'#'} class="btn btn-link p-0 text-secondary"
					><i class="fas fa-stethoscope"></i>
					{locale.T('opd')}
				</a>
			</li>
		</ol>
	</div>
</div>

<div class="row g-1">
	<div class="col-md-12">
		<div class="card rounded-0 bg-light">
			<div class="card-header">
				<div class="row px-2">
					<SearchProductSubmit
						placeholder="ស្វែករកតាមរយៈផលិតផល"
						q_name="q"
						items={get_products.map((e) => ({ id: e.id, name: e.products, price: e.price }))}
						action="?/create_product_order"
						billing_id={get_billing?.id}
					>
						<input type="hidden" value={get_billing?.id} name="billing_id" />
					</SearchProductSubmit>
				</div>
			</div>

			<div style="height: {inerHight}; " class="card-body table-responsive p-0 m-0">
				<table class="table table-bordered table-sm text-nowrap table-light">
					<thead class="sticky-top top-0 bg-light table-active">
						<tr class="text-center">
							<th style="width: 45%;">{locale.T('product_name')}</th>
							<th style="width: 15%;">{locale.T('price')}</th>
							<th style="width: 10%;">{locale.T('qty')}</th>
							<th style="width: 15%;">{locale.T('sub_total')} </th>
							<th style="width: 5%;">X</th>
						</tr>
					</thead>
					<tbody>
						<ChargeService
							data={{
								charge_on_service: charge_on_service,
								get_currency: get_currency
							}}
						/>
						<ChargeLaboratory
							data={{
								charge_on_laboratory: charge_on_laboratory,
								get_currency: get_currency
							}}
						/>
						<ChargeImagerie
							data={{
								charge_on_imagerie: charge_on_imagerie,
								get_currency: get_currency
							}}
						/>
						<ChargePrescription
							data={{
								charge_on_prescription: charge_on_prescription,
								get_currency: get_currency,
								get_billing: get_billing
							}}
						/>
						<ChargeVaccine
							data={{
								charge_on_vaccine: charge_on_vaccine,
								get_currency: get_currency
							}}
						/>
						<ChargeGeneral
							data={{
								charge_on_general: charge_on_general,
								get_currency: get_currency
							}}
						/>
					</tbody>
				</table>
			</div>
			<div class="">
				<div class="border-0 bg-primary-subtle py-1 fs-6 mb-1">
					<div class="row">
						<div class="col text-start mx-2">{locale.T('quantity_of_goods')}</div>
						<div class="col text-end mx-2">{items} {locale.T('items')}</div>
					</div>
				</div>
				<div class=" border-0 bg-primary-subtle py-1 fs-6 mb-1">
					<div class="row">
						<div class="col text-start mx-2">
							{locale.T('total')}
							{get_currency?.exchang_to}
						</div>
						<div class="col text-end mx-2">
							<Currency
								class=""
								{get_currency}
								amount={get_billing?.amount}
								symbol={get_currency?.exchang_to}
							/>
						</div>
					</div>
				</div>
				<div class=" border-0 bg-primary-subtle py-1 fs-6 mb-1">
					<div class="row">
						<div class="col text-start mx-2">
							{locale.T('total')}
							{get_currency?.currency}
						</div>
						<div class="col text-end mx-2">
							<span>
								<Currency class="" amount={get_billing?.amount} symbol={get_currency?.currency} />
							</span>
						</div>
					</div>
				</div>

				{#each get_billings_due as item (item.id)}
					<div class=" border-0 bg-primary-subtle py-1 fs-6 mb-1">
						<div class="row">
							<div class="col text-start mx-2">
								{locale.T('previous_debt')}
								<DDMMYYYYFormat date={item.created_at} style="date" />
							</div>
							<div class="col text-end mx-2">
								<a href="/billing/repay?billing_id={item.id}">
									<Currency class="fs-6" amount={item.balance} symbol={get_currency?.currency} /></a
								>
							</div>
						</div>
					</div>
				{/each}
				<div class="row g-0">
					<div class="col">
						<a href="/billing/report" class="btn rounded-0 btn-primary btn-lg w-100">
							<i class="fa-solid fa-rotate-left"></i>{locale.T('back')}</a
						>
					</div>
					<div class="col">
						<a
							href="/billing/opd/{get_billing?.visit_id}/checkout"
							class="btn rounded-0 btn-success btn-lg w-100"
						>
							<i class="fa-solid fa-comments-dollar"></i> {locale.T('billing')}</a
						>
						<!-- <button
							type="button"
							data-bs-toggle="modal"
							data-bs-target="#billing"
							class="btn rounded-0 btn-success btn-lg w-100"
						>
							<i class="fa-solid fa-comments-dollar"></i> គិតលុយ</button
						> -->
					</div>
				</div>
			</div>
		</div>
	</div>
</div>

<ConfirmModal action="?/hold" id={get_billing?.id} />
